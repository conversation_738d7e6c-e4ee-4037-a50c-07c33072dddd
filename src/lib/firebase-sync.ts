import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  Timestamp,
  DocumentSnapshot,
  QuerySnapshot,
  Unsubscribe,
  DocumentData
} from 'firebase/firestore'
import { db } from './firebase'
import { multiFirebaseManager, FirebaseConnection } from './multi-firebase'
import { ExternalFirebaseConnection, SyncLog, CreateSyncLog } from './database.types'
import { dataSourcesService, getUserDataSources } from './firestore'

interface SyncRule {
  sourceCollection: string
  targetCollection: string
  fieldMappings?: Record<string, string>
  filters?: Array<{
    field: string
    operator: any
    value: any
  }>
  transformations?: Array<{
    field: string
    transform: (value: any) => any
  }>
}

interface SyncSession {
  connectionId: string
  syncRules: SyncRule[]
  listeners: Unsubscribe[]
  isActive: boolean
  lastSync: Date
  stats: {
    documentsProcessed: number
    documentsSuccess: number
    documentsFailed: number
    errors: string[]
  }
}

/**
 * Real-time Firebase Data Sync Engine
 * Handles bidirectional data synchronization between Firebase projects
 */
export class FirebaseSyncEngine {
  private activeSessions: Map<string, SyncSession> = new Map()
  private static instance: FirebaseSyncEngine | null = null

  private constructor() {}

  static getInstance(): FirebaseSyncEngine {
    if (!FirebaseSyncEngine.instance) {
      FirebaseSyncEngine.instance = new FirebaseSyncEngine()
    }
    return FirebaseSyncEngine.instance
  }

  /**
   * Start real-time sync for a connection
   */
  async startSync(
    connectionId: string,
    syncRules: SyncRule[],
    direction: 'pull' | 'push' | 'bidirectional' = 'pull'
  ): Promise<boolean> {
    try {
      // Get the external connection
      const connection = multiFirebaseManager.getConnection(connectionId)
      if (!connection) {
        throw new Error(`Connection ${connectionId} not found`)
      }

      // Stop existing sync if running
      await this.stopSync(connectionId)

      // Create sync session
      const session: SyncSession = {
        connectionId,
        syncRules,
        listeners: [],
        isActive: true,
        lastSync: new Date(),
        stats: {
          documentsProcessed: 0,
          documentsSuccess: 0,
          documentsFailed: 0,
          errors: []
        }
      }

      // Set up listeners based on sync direction
      if (direction === 'pull' || direction === 'bidirectional') {
        await this.setupPullListeners(connection, session)
      }

      if (direction === 'push' || direction === 'bidirectional') {
        await this.setupPushListeners(connection, session)
      }

      // Store active session
      this.activeSessions.set(connectionId, session)

      // Log sync start
      await this.logSyncEvent(connectionId, {
        sync_type: direction,
        status: 'started',
        collections_synced: syncRules.map(rule => rule.sourceCollection),
        started_at: new Date().toISOString()
      })

      console.log(`✅ Started ${direction} sync for connection: ${connectionId}`)
      return true

    } catch (error) {
      console.error(`❌ Failed to start sync for connection ${connectionId}:`, error)
      return false
    }
  }

  /**
   * Stop sync for a connection
   */
  async stopSync(connectionId: string): Promise<boolean> {
    try {
      const session = this.activeSessions.get(connectionId)
      if (!session) {
        return true // Already stopped
      }

      // Unsubscribe from all listeners
      session.listeners.forEach(unsubscribe => unsubscribe())
      session.isActive = false

      // Remove from active sessions
      this.activeSessions.delete(connectionId)

      console.log(`✅ Stopped sync for connection: ${connectionId}`)
      return true

    } catch (error) {
      console.error(`❌ Failed to stop sync for connection ${connectionId}:`, error)
      return false
    }
  }

  /**
   * Set up pull listeners (external -> local)
   */
  private async setupPullListeners(connection: FirebaseConnection, session: SyncSession): Promise<void> {
    for (const rule of session.syncRules) {
      try {
        // Create query for external collection
        let externalQuery: any = collection(connection.db, rule.sourceCollection)
        
        // Apply filters if specified
        if (rule.filters) {
          for (const filter of rule.filters) {
            externalQuery = query(externalQuery, where(filter.field, filter.operator, filter.value))
          }
        }

        // Set up real-time listener
        const unsubscribe = onSnapshot(
          externalQuery,
          (snapshot: QuerySnapshot) => {
            this.handlePullSnapshot(snapshot, rule, session)
          },
          (error) => {
            console.error(`Pull listener error for ${rule.sourceCollection}:`, error)
            session.stats.errors.push(`Pull error: ${error.message}`)
          }
        )

        session.listeners.push(unsubscribe)

      } catch (error) {
        console.error(`Failed to setup pull listener for ${rule.sourceCollection}:`, error)
        session.stats.errors.push(`Setup error: ${error.message}`)
      }
    }
  }

  /**
   * Set up push listeners (local -> external)
   */
  private async setupPushListeners(connection: FirebaseConnection, session: SyncSession): Promise<void> {
    for (const rule of session.syncRules) {
      try {
        // Create query for local collection
        let localQuery = collection(db, rule.sourceCollection)
        
        // Apply filters if specified
        if (rule.filters) {
          for (const filter of rule.filters) {
            localQuery = query(localQuery, where(filter.field, filter.operator, filter.value))
          }
        }

        // Set up real-time listener
        const unsubscribe = onSnapshot(
          localQuery,
          (snapshot: QuerySnapshot) => {
            this.handlePushSnapshot(snapshot, rule, session, connection)
          },
          (error) => {
            console.error(`Push listener error for ${rule.sourceCollection}:`, error)
            session.stats.errors.push(`Push error: ${error.message}`)
          }
        )

        session.listeners.push(unsubscribe)

      } catch (error) {
        console.error(`Failed to setup push listener for ${rule.sourceCollection}:`, error)
        session.stats.errors.push(`Setup error: ${error.message}`)
      }
    }
  }

  /**
   * Handle pull snapshot changes (external -> local)
   */
  private async handlePullSnapshot(
    snapshot: QuerySnapshot,
    rule: SyncRule,
    session: SyncSession
  ): Promise<void> {
    const batch = writeBatch(db)
    let batchCount = 0
    const maxBatchSize = 500

    for (const change of snapshot.docChanges()) {
      try {
        const sourceDoc = change.doc
        const sourceData = sourceDoc.data()

        // Transform data according to rule
        const transformedData = this.transformData(sourceData, rule)

        // Add sync metadata
        transformedData._sync_source = session.connectionId
        transformedData._sync_timestamp = serverTimestamp()
        transformedData._sync_original_id = sourceDoc.id

        const targetDocRef = doc(db, rule.targetCollection, sourceDoc.id)

        if (change.type === 'added' || change.type === 'modified') {
          batch.set(targetDocRef, transformedData, { merge: true })
        } else if (change.type === 'removed') {
          batch.delete(targetDocRef)
        }

        batchCount++
        session.stats.documentsProcessed++

        // Commit batch if it reaches max size
        if (batchCount >= maxBatchSize) {
          await batch.commit()
          batchCount = 0
        }

      } catch (error) {
        console.error(`Error processing pull change for ${rule.sourceCollection}:`, error)
        session.stats.documentsFailed++
        session.stats.errors.push(`Pull processing error: ${error.message}`)
      }
    }

    // Commit remaining batch
    if (batchCount > 0) {
      try {
        await batch.commit()
        session.stats.documentsSuccess += batchCount
        console.log(`✅ Pull batch committed: ${batchCount} documents processed`)
      } catch (error) {
        console.error(`Error committing pull batch:`, error)
        session.stats.documentsFailed += batchCount
        session.stats.errors.push(`Pull batch commit error: ${error.message}`)
      }
    }

    session.lastSync = new Date()

    // Log completion if documents were processed
    if (session.stats.documentsProcessed > 0) {
      await this.logSyncEvent(session.connectionId, {
        sync_type: 'pull',
        status: 'completed',
        collections_synced: [rule.sourceCollection],
        records_processed: session.stats.documentsProcessed,
        records_success: session.stats.documentsSuccess,
        records_failed: session.stats.documentsFailed,
        completed_at: new Date().toISOString(),
        sync_duration_ms: Date.now() - session.lastSync.getTime()
      })
    }
  }

  /**
   * Handle push snapshot changes (local -> external)
   */
  private async handlePushSnapshot(
    snapshot: QuerySnapshot,
    rule: SyncRule,
    session: SyncSession,
    connection: FirebaseConnection
  ): Promise<void> {
    const batch = writeBatch(connection.db)
    let batchCount = 0
    const maxBatchSize = 500

    for (const change of snapshot.docChanges()) {
      try {
        const sourceDoc = change.doc
        const sourceData = sourceDoc.data()

        // Skip if this document was synced from external source
        if (sourceData._sync_source === session.connectionId) {
          continue
        }

        // Transform data according to rule
        const transformedData = this.transformData(sourceData, rule)

        // Add sync metadata
        transformedData._sync_source = 'local'
        transformedData._sync_timestamp = serverTimestamp()

        const targetDocRef = doc(connection.db, rule.targetCollection, sourceDoc.id)

        if (change.type === 'added' || change.type === 'modified') {
          batch.set(targetDocRef, transformedData, { merge: true })
        } else if (change.type === 'removed') {
          batch.delete(targetDocRef)
        }

        batchCount++
        session.stats.documentsProcessed++

        // Commit batch if it reaches max size
        if (batchCount >= maxBatchSize) {
          await batch.commit()
          batchCount = 0
        }

      } catch (error) {
        console.error(`Error processing push change for ${rule.sourceCollection}:`, error)
        session.stats.documentsFailed++
        session.stats.errors.push(`Push processing error: ${error.message}`)
      }
    }

    // Commit remaining batch
    if (batchCount > 0) {
      try {
        await batch.commit()
        session.stats.documentsSuccess += batchCount
        console.log(`✅ Push batch committed: ${batchCount} documents processed`)
      } catch (error) {
        console.error(`Error committing push batch:`, error)
        session.stats.documentsFailed += batchCount
        session.stats.errors.push(`Push batch commit error: ${error.message}`)
      }
    }

    session.lastSync = new Date()

    // Log completion if documents were processed
    if (session.stats.documentsProcessed > 0) {
      await this.logSyncEvent(session.connectionId, {
        sync_type: 'push',
        status: 'completed',
        collections_synced: [rule.sourceCollection],
        records_processed: session.stats.documentsProcessed,
        records_success: session.stats.documentsSuccess,
        records_failed: session.stats.documentsFailed,
        completed_at: new Date().toISOString(),
        sync_duration_ms: Date.now() - session.lastSync.getTime()
      })
    }
  }

  /**
   * Transform data according to sync rule
   */
  private transformData(data: DocumentData, rule: SyncRule): DocumentData {
    let transformedData = { ...data }

    // Apply field mappings
    if (rule.fieldMappings) {
      const mappedData: DocumentData = {}
      Object.entries(rule.fieldMappings).forEach(([sourceField, targetField]) => {
        if (data[sourceField] !== undefined) {
          mappedData[targetField] = data[sourceField]
        }
      })
      transformedData = { ...transformedData, ...mappedData }
    }

    // Apply transformations
    if (rule.transformations) {
      rule.transformations.forEach(({ field, transform }) => {
        if (transformedData[field] !== undefined) {
          try {
            transformedData[field] = transform(transformedData[field])
          } catch (error) {
            console.warn(`Transformation failed for field ${field}:`, error)
          }
        }
      })
    }

    return transformedData
  }

  /**
   * Log sync events
   */
  private async logSyncEvent(connectionId: string, logData: Partial<CreateSyncLog>): Promise<void> {
    try {
      const connection = multiFirebaseManager.getConnection(connectionId)
      if (!connection) {
        console.error(`Connection ${connectionId} not found for logging sync event`)
        return
      }

      console.log(`📝 Logging sync event for connection ${connectionId}:`, {
        sync_type: logData.sync_type,
        status: logData.status,
        collections: logData.collections_synced,
        records: logData.records_processed
      })

      const syncLog: CreateSyncLog = {
        connection_id: connectionId,
        user_id: connection.config.user_id,
        sync_type: logData.sync_type || 'pull',
        status: logData.status || 'started',
        collections_synced: logData.collections_synced || [],
        records_processed: logData.records_processed || 0,
        records_success: logData.records_success || 0,
        records_failed: logData.records_failed || 0,
        error_message: logData.error_message || null,
        sync_duration_ms: logData.sync_duration_ms || 0,
        started_at: logData.started_at || new Date().toISOString(),
        completed_at: logData.completed_at || null,
        details: logData.details || null
      }

      console.log(`💾 Creating sync log in database:`, syncLog)
      await addDoc(collection(db, 'sync_logs'), syncLog)
      console.log(`✅ Sync log created successfully`)

      // Update corresponding DataSource if sync completed successfully
      if (logData.status === 'completed' && logData.records_processed && logData.records_processed > 0) {
        await this.updateDataSourceFromSync(connectionId, logData)
      }

    } catch (error) {
      console.error('Failed to log sync event:', error)
    }
  }

  /**
   * Update the corresponding DataSource when sync completes
   */
  private async updateDataSourceFromSync(connectionId: string, logData: Partial<CreateSyncLog>): Promise<void> {
    try {
      const connection = multiFirebaseManager.getConnection(connectionId)
      if (!connection) return

      console.log(`🔄 Updating DataSource for connection ${connectionId}`)

      // Find the DataSource associated with this connection
      const userDataSources = await getUserDataSources(connection.config.user_id)
      const firebaseDataSource = userDataSources.find(ds =>
        ds.type === 'firebase_sync' &&
        ds.metadata?.connection_id === connectionId
      )

      if (!firebaseDataSource) {
        console.log(`No DataSource found for connection ${connectionId}`)
        return
      }

      // Update the DataSource with latest sync information
      const updatedConfig = {
        ...firebaseDataSource.config,
        record_count: logData.records_processed || 0,
        last_sync_at: new Date().toISOString(),
        collections_synced: logData.collections_synced || [],
        sync_status: logData.status
      }

      await dataSourcesService.update(firebaseDataSource.id, {
        config: updatedConfig,
        last_sync: new Date().toISOString(),
        status: logData.status === 'completed' ? 'active' : 'error',
        metadata: {
          ...firebaseDataSource.metadata,
          last_sync_records: logData.records_processed || 0,
          last_sync_success: logData.records_success || 0,
          last_sync_failed: logData.records_failed || 0,
          last_sync_duration_ms: logData.sync_duration_ms || 0
        }
      })

      console.log(`✅ Updated DataSource ${firebaseDataSource.id} with sync results`)

    } catch (error) {
      console.error('Failed to update DataSource from sync:', error)
    }
  }

  /**
   * Get sync status for a connection
   */
  getSyncStatus(connectionId: string): {
    isActive: boolean
    lastSync?: Date
    stats?: SyncSession['stats']
  } {
    const session = this.activeSessions.get(connectionId)
    if (!session) {
      return { isActive: false }
    }

    return {
      isActive: session.isActive,
      lastSync: session.lastSync,
      stats: session.stats
    }
  }

  /**
   * Get all active sync sessions
   */
  getActiveSessions(): Map<string, SyncSession> {
    return new Map(this.activeSessions)
  }

  /**
   * Cleanup all sync sessions
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up sync sessions...')
    
    const connectionIds = Array.from(this.activeSessions.keys())
    for (const connectionId of connectionIds) {
      await this.stopSync(connectionId)
    }
    
    console.log('✅ All sync sessions cleaned up')
  }
}

// Export singleton instance
export const firebaseSyncEngine = FirebaseSyncEngine.getInstance()

// Export types
export type { SyncRule, SyncSession }
